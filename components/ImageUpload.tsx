"use client";

import React, { useEffect, useState } from "react";
import { Upload, message, Modal } from "antd";
import { FileImageOutlined, HddOutlined } from "@ant-design/icons";
import type { UploadProps, UploadFile, RcFile } from "antd/es/upload";

interface ImageUploadProps {
  onFileSelect: (file: File) => void;
  onRemoveFile: () => void;
  selectedFile: File | null;
  disabled?: boolean;
}

const ACCEPTED_TYPES = ["image/png", "image/jpeg", "image/jpg", "image/webp"];
const MAX_SIZE = 5 * 1024 * 1024; // 5MB

export default function ImageUpload({
  onFileSelect,
  onRemoveFile,
  selectedFile,
  disabled = false,
}: ImageUploadProps) {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");

  // Update fileList when selectedFile changes
  useEffect(() => {
    if (selectedFile) {
      const uploadFile: UploadFile = {
        uid: "uploaded-image",
        name: selectedFile.name,
        status: "done",
        url: URL.createObjectURL(selectedFile),
        originFileObj: selectedFile as RcFile,
      };
      setFileList([uploadFile]);
    } else {
      setFileList([]);
    }
  }, [selectedFile]);

  const validateFile = (file: File): boolean => {
    if (!ACCEPTED_TYPES.includes(file.type)) {
      message.error("Please upload a valid image file (PNG, JPG, JPEG, WebP)");
      return false;
    }

    if (file.size > MAX_SIZE) {
      message.error("File size must be less than 5MB");
      return false;
    }

    return true;
  };

  const handleChange: UploadProps["onChange"] = (info) => {
    const { fileList: newFileList } = info;

    // Only keep the latest file (replace old one)
    if (newFileList.length > 0) {
      const latestFile = newFileList[newFileList.length - 1];
      if (latestFile.originFileObj && validateFile(latestFile.originFileObj)) {
        onFileSelect(latestFile.originFileObj);
      }
    }
  };

  const handleRemove = () => {
    onRemoveFile();
    setFileList([]);
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      if (file.originFileObj) {
        file.preview = URL.createObjectURL(file.originFileObj);
      }
    }
    setPreviewImage(file.url || file.preview || "");
    setPreviewOpen(true);
  };

  return (
    <div className="space-y-4">
      {/* Single Upload component that handles both empty and filled states */}
      <Upload
        name="file"
        listType="picture-card"
        fileList={fileList}
        onPreview={handlePreview}
        onChange={handleChange}
        onRemove={handleRemove}
        beforeUpload={() => false} // Prevent auto upload
        accept={ACCEPTED_TYPES.join(",")}
        disabled={disabled}
        maxCount={1}
        pastable={!disabled}
        className="custom-upload"
      >
        {fileList.length >= 1 ? null : (
          <div className="flex flex-col items-center justify-center p-8 min-h-[200px] w-full">
            <div className="flex justify-center mb-4"></div>

            <div className="space-y-2 text-center">
              <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Upload your code screenshot
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Paste, drop, or click to upload
              </p>
            </div>

            <div className="flex items-center justify-center gap-4 text-xs text-gray-400 dark:text-gray-500 mt-4">
              <span className="flex items-center gap-1">
                <FileImageOutlined /> PNG, JPG, WebP
              </span>
              <span>
                <HddOutlined /> Max 5MB
              </span>
            </div>
          </div>
        )}
      </Upload>

      {/* Preview Modal */}
      <Modal
        open={previewOpen}
        title="Image Preview"
        footer={null}
        onCancel={() => setPreviewOpen(false)}
        width="80%"
        style={{ maxWidth: "800px" }}
      >
        <img
          alt="preview"
          style={{
            width: "100%",
            maxHeight: "70vh",
            objectFit: "contain",
          }}
          src={previewImage}
        />
      </Modal>

      <style jsx global>{`
        .custom-upload .ant-upload-select {
          width: 100% !important;
          min-height: 200px !important;
        }
        .custom-upload .ant-upload-select .ant-upload {
          width: 100% !important;
          height: 100% !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }
      `}</style>
    </div>
  );
}
