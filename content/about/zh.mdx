---
title: 关于
description: 关于 Next Forge 多语言启动模板
lastUpdated: 2025-02-19
---

> 更新日期：2025-02-19

# 关于 Next Forge

Next Forge 是一个功能完备的 Next.js 15 多语言启动模板，旨在帮助开发者快速构建面向全球的网站。它内置了多语言支持、现代 UI 设计、深色/浅色主题切换、响应式布局、MDX 博客系统、SEO 优化以及多种统计分析工具。

## ✨ 主要特性

- 🌐 **多语言支持**：内置中文、英文、日语的国际化支持，轻松实现多语言网站。
- 🎨 **现代 UI 设计**：基于 Tailwind CSS 的现代 UI 设计，简洁美观。
- 🌙 **深色/浅色主题切换**：支持用户自由切换深色和浅色主题。
- 📱 **响应式布局**：适配各种设备，确保在移动端和桌面端都有良好的用户体验。
- 📝 **MDX 博客系统**：支持使用 MDX 编写博客文章，灵活且强大。
- 🔍 **SEO 优化**：内置完整的 SEO 优化方案，包括自动生成 sitemap.xml 和 robots.txt。
- 📊 **统计分析**：集成 Google Analytics、Baidu Analytics、Google Adsense 和 Vercel Analytics，方便进行数据追踪。
- 🌿 **环保性能**：在 [Website Carbon](https://www.websitecarbon.com/website/nextforge-dev/) 上获得 A+ 评级，成为最节能的网站之一。

## 🚀 快速开始

1. 克隆项目：

   ```bash
   git clone https://github.com/weijunext/nextjs-15-starter.git
   ```

2. 安装依赖：

   ```bash
   npm install
   ```

3. 复制环境变量文件：

   ```bash
   cp .env.example .env
   ```

4. 启动开发服务器：
   ```bash
   npm run dev
   ```

访问 [http://localhost:3000](http://localhost:3000) 查看你的应用。

## ⚙️ 配置

1. **基础配置**：

   - 修改 `config/site.ts` 配置网站信息。
   - 更新 `public/` 下的图标和 logo。
   - 配置 `app/sitemap.ts` 和 `app/robots.ts`。

2. **多语言配置**：
   - 在 `i18n/messages/` 下添加或修改语言文件。
   - 在 `i18n/routing.ts` 中配置支持的语言。
   - 在 `middleware.ts` 中配置多语言路由。

## 📝 内容管理

### 博客文章

在 `blogs/[locale]` 目录下创建 MDX 文件，支持以下格式：

```markdown
---
title: 文章标题
description: 文章描述
image: /image.png
slug: /url-path
tags: tag1,tag2
date: 2025-02-20
visible: published
pin: true
---

文章内容...
```

### 静态页面

在 `content/[page]/[locale].mdx` 下管理静态页面内容。

## 📄 许可证

Next Forge 采用 MIT 许可证，您可以自由使用、修改和分发。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！您的贡献将帮助我们不断改进这个项目。

## 关于作者

专注于 Next.js 全栈开发，欢迎探讨开发、咨询与培训等合作机会，联系微信：bigye_chengpuflex-col

- [Github](https://github.com/weijunext)
- [Twitter/X](https://twitter.com/weijunext)
- [博客 - J 实验室](https://weijunext.com)
- [Medium](https://medium.com/@weijunext)
- [掘金](https://juejin.cn/user/26044008768029)
- [知乎](https://www.zhihu.com/people/mo-mo-mo-89-12-11)
